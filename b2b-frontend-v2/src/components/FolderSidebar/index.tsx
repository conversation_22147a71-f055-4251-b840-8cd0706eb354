import React from "react";
import {
  ChevronDownIcon,
  PlusIcon,
  VerticalElipsisIcon,
} from "@/assets/images/svgs/common";
import { FolderI, FolderSidebarProps } from "./types";
import Search from "../Search";

const FolderSidebar: React.FC<FolderSidebarProps> = ({
  folders,
  selectedFolder,
  onFolderSelect,
}) => {
  return (
    <div className="w-64 bg-white border-r border-neutral-200 p-4">
      <div className="py-2 mb-2">
        <Search />
      </div>

      {/* Folders Header */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="flex items-center gap-1 font-semibold text-lg cursor-pointer">
          Folders
          <PlusIcon height={20} width={20} />
        </h3>
      </div>

      {/* Folders List */}
      <div className="space-y-1">
        {folders.map((folder: FolderI) => (
          <div
            key={folder.id}
            className={`flex items-center justify-between px-3 py-1 rounded-lg cursor-pointer transition-colors ${
              selectedFolder === folder.name
                ? "bg-primary-50 border border-primary-200"
                : "hover:bg-neutral-50"
            }`}
            onClick={() => onFolderSelect(folder.name)}
          >
            <div className="flex items-center gap-3">
              <span
                className={`text-sm font-medium ${
                  selectedFolder === folder.name
                    ? "text-primary-700"
                    : "text-neutral-700"
                }`}
              >
                {folder.name}
              </span>
            </div>
            <div className="flex items-center gap-2">
              {/* Three dots menu */}
              <VerticalElipsisIcon />
              {/* Chevron */}
              <ChevronDownIcon />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default FolderSidebar;
