import React from "react";
import { DocumentGridProps } from "./types";
import DocumentCard from "../DocumentCard";

const DocumentGrid: React.FC<DocumentGridProps> = ({ documents }) => {
  if (documents.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <div className="text-neutral-400 mb-2">
          <svg
            width="48"
            height="48"
            viewBox="0 0 48 48"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M12 8h24v32H12V8z"
              stroke="currentColor"
              strokeWidth="2"
              fill="none"
            />
            <path
              d="M16 16h16M16 20h16M16 24h12"
              stroke="currentColor"
              strokeWidth="2"
            />
          </svg>
        </div>
        <p className="text-neutral-500 text-lg font-medium">No documents found</p>
        <p className="text-neutral-400 text-sm">
          Try adjusting your search or selecting a different folder
        </p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
      {documents.map((document) => (
        <DocumentCard key={document.id} document={document} />
      ))}
    </div>
  );
};

export default DocumentGrid;
