import React from "react";
import DocumentCard from "../DocumentCard";
import { DocumentGridPropsI } from "./types";
import { DocumentI } from "../DocumentCard/types";

const DocumentGrid: React.FC<DocumentGridPropsI> = ({
  documents,
  selectedDocuments = [],
  onDocumentSelect
}) => {
  if (documents.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <p className="text-neutral-500 text-lg font-medium">
          No documents found
        </p>
        <p className="text-neutral-400 text-sm">
          Try adjusting your search or selecting a different folder
        </p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
      {documents.map((document: DocumentI) => (
        <DocumentCard
          key={document.id}
          document={document}
          isSelected={selectedDocuments.includes(document.id)}
          onSelect={onDocumentSelect}
        />
      ))}
    </div>
  );
};

export default DocumentGrid;
