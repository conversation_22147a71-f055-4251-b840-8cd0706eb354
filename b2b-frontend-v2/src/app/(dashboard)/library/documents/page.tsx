"use client";
import DashboardHeader from "@/components/DashboardHeader";
import DocumentGrid from "@/components/DocumentGrid";
import FolderSidebar from "@/components/FolderSidebar";
import React, { useState } from "react";
import { DocumentI } from "@/components/DocumentCard/types";

const LibraryPage = () => {
  const [selectedFolder, setSelectedFolder] = useState<string | null>(
    "Folder 1",
  );
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);

  const folders = [
    { id: "1", name: "Folder 1", count: 5 },
    { id: "2", name: "Folder 2", count: 3 },
    { id: "3", name: "Folder 3", count: 8 },
    { id: "4", name: "Folder 4", count: 2 },
    { id: "5", name: "Folder 5", count: 12 },
    { id: "6", name: "Folder 6", count: 7 },
  ];

  const documents = [
    { id: "1", name: "Document Name 1", type: "word", folderId: "1" },
    { id: "2", name: "Document Name 1", type: "pdf", folderId: "1" },
    { id: "3", name: "Document Name 1", type: "pdf", folderId: "1" },
    { id: "4", name: "Document Name 1", type: "pdf", folderId: "1" },
    { id: "5", name: "Document Name 1", type: "word", folderId: "1" },
    { id: "6", name: "Document Name 2", type: "word", folderId: "2" },
    { id: "7", name: "Document Name 3", type: "pdf", folderId: "3" },
  ];

  const handleDocumentSelect = (document: DocumentI) => {
    setSelectedDocuments(prev => {
      if (prev.includes(document.id)) {
        // Deselect if already selected
        return prev.filter(id => id !== document.id);
      } else {
        // Select the document (allow multiple selection)
        return [...prev, document.id];
      }
    });
  };

  return (
    <div className="flex flex-1 flex-col">
      <DashboardHeader
        title="Documents"
        description="Manage and organize your document collection"
        actions={[
          {
            label: "Edit Document",
            variant: "ghost",
            className:
              "!px-4 !py-2 !text-sm !font-medium !border-neutral-300 !text-neutral-700 hover:!bg-neutral-50 !w-auto",
            onClick: () => console.log("Edit Document"),
          },
          {
            label: "Upload Document",
            variant: "primary",
            className: "!px-4 !py-2 !text-sm !font-medium !text-white !w-auto",
            onClick: () => console.log("Upload Document"),
          },
        ]}
      />

      <div className="flex flex-1 bg-neutral-50">
        <FolderSidebar
          folders={folders}
          selectedFolder={selectedFolder}
          onFolderSelect={setSelectedFolder}
        />

        <div className="flex-1 p-6">
          {/* Selection indicator */}
          {selectedDocuments.length > 0 && (
            <div className="mb-4 p-3 bg-primary-50 border border-primary-200 rounded-lg">
              <span className="text-primary-700 font-medium">
                {selectedDocuments.length} document{selectedDocuments.length > 1 ? 's' : ''} selected
              </span>
            </div>
          )}

          <DocumentGrid
            documents={documents}
            selectedDocuments={selectedDocuments}
            onDocumentSelect={handleDocumentSelect}
          />
        </div>
      </div>
    </div>
  );
};

export default LibraryPage;
