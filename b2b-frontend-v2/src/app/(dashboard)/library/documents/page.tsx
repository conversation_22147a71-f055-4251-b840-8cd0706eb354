"use client";
import DashboardHeader from "@/components/DashboardHeader";
import React from "react";

const LibraryPage = () => {
  return (
    <div className="flex flex-1 flex-col">
      <DashboardHeader
        title="Documents"
        description="Manage and organize your document collection"
        actions={[
          {
            label: "Edit Document",
            variant: "ghost",
            className:
              "!px-4 !py-2 !text-sm !font-medium !border-neutral-300 !text-neutral-700 hover:!bg-neutral-50 !w-auto",
            onClick: () => console.log("Edit Document"),
          },
          {
            label: "Upload Document",
            variant: "primary",
            className: "!px-4 !py-2 !text-sm !font-medium !text-white !w-auto",
            onClick: () => console.log("Upload Document"),
          },
        ]}
      />

      
    </div>
  );
};

export default LibraryPage;
