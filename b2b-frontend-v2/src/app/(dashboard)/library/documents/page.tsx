"use client";
import DashboardHeader from "@/components/DashboardHeader";
import DocumentGrid from "@/components/DocumentGrid";
import FolderSidebar from "@/components/FolderSidebar";
import Search from "@/components/Search";
import React, { useState } from "react";

const LibraryPage = () => {
  const [selectedFolder, setSelectedFolder] = useState<string | null>("Folder 1");
  const [searchTerm, setSearchTerm] = useState<string>("");

  // Mock data for folders
  const folders = [
    { id: "1", name: "Folder 1", count: 5 },
    { id: "2", name: "Folder 2", count: 3 },
    { id: "3", name: "Folder 3", count: 8 },
    { id: "4", name: "Folder 4", count: 2 },
    { id: "5", name: "Folder 5", count: 12 },
    { id: "6", name: "Folder 6", count: 7 },
  ];

  // Mock data for documents
  const documents = [
    { id: "1", name: "Document Name 1", type: "word", folderId: "1" },
    { id: "2", name: "Document Name 1", type: "pdf", folderId: "1" },
    { id: "3", name: "Document Name 1", type: "pdf", folderId: "1" },
    { id: "4", name: "Document Name 1", type: "pdf", folderId: "1" },
    { id: "5", name: "Document Name 1", type: "word", folderId: "1" },
    { id: "6", name: "Document Name 2", type: "word", folderId: "2" },
    { id: "7", name: "Document Name 3", type: "pdf", folderId: "3" },
  ];

  const handleSearch = (searchValue: string | undefined) => {
    setSearchTerm(searchValue || "");
  };

  const filteredDocuments = documents.filter(doc => {
    const matchesFolder = selectedFolder ? doc.folderId === selectedFolder.split(" ")[1] : true;
    const matchesSearch = searchTerm ? doc.name.toLowerCase().includes(searchTerm.toLowerCase()) : true;
    return matchesFolder && matchesSearch;
  });

  return (
    <div className="flex flex-1 flex-col">
      <DashboardHeader
        title="Documents"
        description="Manage and organize your document collection"
        actions={[
          {
            label: "Edit Document",
            variant: "ghost",
            className:
              "!px-4 !py-2 !text-sm !font-medium !border-neutral-300 !text-neutral-700 hover:!bg-neutral-50 !w-auto",
            onClick: () => console.log("Edit Document"),
          },
          {
            label: "Upload Document",
            variant: "primary",
            className: "!px-4 !py-2 !text-sm !font-medium !text-white !w-auto",
            onClick: () => console.log("Upload Document"),
          },
        ]}
      />

      <div className="flex flex-1 bg-neutral-50">
        <FolderSidebar
          folders={folders}
          selectedFolder={selectedFolder}
          onFolderSelect={setSelectedFolder}
        />

        <div className="flex-1 p-6">

          <DocumentGrid documents={filteredDocuments} />
        </div>
      </div>
    </div>
  );
};

export default LibraryPage;
